import RForm from '@/components/RForm';
import RInput from '@/components/RInput';
import SearchForm from '@/components/SearchForm';
import { GiftQuerySearchFormValues } from '@/types/gift';

export default function SearchFormGiftQuery({
  onSearch,
  onReset
}: {
  onSearch: (values: GiftQuerySearchFormValues) => void;
  onReset: () => void;
}) {
  const { t } = useTranslation();

  const handleSearch = (values: GiftQuerySearchFormValues) => {
    onSearch(values);
  };

  return (
    <SearchForm<GiftQuerySearchFormValues> onSearch={handleSearch} onReset={onReset}>
      <>
        <RForm.Item name="account" label={t('pages_transaction_accountGiftQuery_account')}>
          <RInput
            className="h-[32px]"
            placeholder={t('common_please_enter', {
              name: t('pages_transaction_accountGiftQuery_account')
            })}
          />
        </RForm.Item>
        <div className="basis-full" />
      </>
    </SearchForm>
  );
}
