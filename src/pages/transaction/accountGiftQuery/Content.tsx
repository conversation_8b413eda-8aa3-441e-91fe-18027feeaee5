import { useAsset } from '@/hooks/useAsset';
import { GiftQueryResult } from '@/types/gift';

type GiftQueryContentProps = {
  isSearched?: boolean;
  data?: GiftQueryResult;
};

export default function GiftQueryContent({ isSearched }: GiftQueryContentProps) {
  const isNoData = isSearched && false;
  const placeholderImage = useAsset('account_gift_query_placeholder.webp');
  const noDataImage = useAsset('account_gift_query_no_data.webp');

  if (!isSearched) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <img src={placeholderImage} alt="403" width={160} height={160} className="mb-6" />
        <div>請輸入玩家帳號進行查詢</div>
      </div>
    );
  }

  if (isNoData) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <img src={noDataImage} alt="403" width={160} height={160} className="mb-6" />
        <div>玩家帳號不存在</div>
      </div>
    );
  }

  return <div>Gift Query Content</div>;
}
