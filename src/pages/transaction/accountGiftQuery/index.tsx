import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import TableSearchLayout from '@/layout/TableSearchLayout';
import { GiftQuerySearchFormValues } from '@/types/gift';

import GiftQueryContent from './Content';
import SearchFormGiftQuery from './SearchForm';

const AccountGiftQueryPage = () => {
  const { t } = useTranslation();
  const [params, setParams] = useState<{
    account?: string;
  }>({
    account: undefined
  });

  const handleSearch = (newParams: GiftQuerySearchFormValues) => {
    setParams(newParams);
  };

  const handleReset = () => {
    const resetParams = {
      account: undefined
    };
    setParams(resetParams);
  };

  return (
    <TableSearchLayout
      searchFields={<SearchFormGiftQuery onSearch={handleSearch} onReset={handleReset} />}
    >
      <GiftQueryContent isSearched={true} data={undefined} />
    </TableSearchLayout>
  );
};

export default AccountGiftQueryPage;
