export interface GiftSettingVip {
  id: number;
  reserveAmount: string;
  minGiftAmount: string;
  giftTimes: number;
  giftQuotas: string;
  giftFeeRate: string;
}

export interface GiftSetting {
  receiveGiftVipLevel: number;
  giveGiftVipLevel: number;
  receiveGiftTimeLimit: number;
  receiveGiftFrozenRemainTimeLimit: number;
  vipSetting: GiftSettingVip[];
}

export enum GiftStatusFilter {
  INCOMPLETE = 'incomplete',
  COMPLETE = 'complete'
}

// Gift Status interface for API response
export interface GiftStatus {
  id: string;
  label: string;
}

// Incomplete Gift Order interface based on API documentation
export interface IncompleteGiftOrder {
  id: string;
  playerId: number;
  playerAccount: string;
  playerName: string;
  targetId: number;
  targetAccount: string;
  targetName: string;
  item: string;
  todayTimes: number;
  totalTimes: number;
  status: number;
  createdAt: number;
  updatedAt: number;
  updatedBy: string;
}

// Completed Gift Order interface based on API documentation
export interface CompletedGiftOrder {
  id: string;
  playerId: number;
  playerAccount: string;
  playerName: string;
  targetId: number;
  targetAccount: string;
  targetName: string;
  item: string;
  todayTimes: number;
  totalTimes: number;
  status: number;
  note: {
    zhTw: string;
  };
  createdAt: number;
  updatedAt: number;
  updatedBy: string;
}

// Search parameters for gift orders API
export interface GiftOrderSearchParams {
  timeFilter?: 'updated_at' | 'created_at';
  timeStart?: number;
  timeEnd?: number;
  playerAccount?: string;
  targetAccount?: string;
  giftId?: string;
  status?: string;
  excludeTest?: 0 | 1;
  page: number;
  limit: number;
  orderBy?: string;
  sortBy?: 'asc' | 'desc';
}

// Search parameters for completed gift orders API
export interface CompletedGiftOrderSearchParams {
  timeFilter?: 'updated_at' | 'created_at';
  timeStart?: number;
  timeEnd?: number;
  playerAccount?: string;
  targetAccount?: string;
  giftId?: string;
  status?: string;
  excludeTest?: 0 | 1;
  page: number;
  limit: number;
  orderBy?: string;
  sortBy?: 'asc' | 'desc';
}

export type GiftQuerySearchFormValues = {
  account: string;
};

export type GiftQueryResult = {
  data: AccountGiftQueryRecord[];
  total: number;
};

export type AccountGiftQueryRecord = {
  id: string;
  createdAt: number;
  account: string;
  nickname: string;
  giftAccount: string;
  giftNickname: string;
  giftType: string;
  amount: number;
  status: string;
  note: string;
};